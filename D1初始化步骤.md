# D1 数据库初始化步骤

请在 Cloudflare D1 控制台中按顺序执行以下 SQL 语句。每次只执行一个语句。

## 步骤 1: 创建用户表

```sql
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    is_admin INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 步骤 2: 创建收藏表

```sql
CREATE TABLE IF NOT EXISTS favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    movie_id TEXT NOT NULL,
    title TEXT NOT NULL,
    poster TEXT,
    year TEXT,
    type TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    UNIQUE(user_id, movie_id)
);
```

## 步骤 3: 创建播放记录表

```sql
CREATE TABLE IF NOT EXISTS watch_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    movie_id TEXT NOT NULL,
    title TEXT NOT NULL,
    poster TEXT,
    year TEXT,
    type TEXT,
    episode TEXT,
    progress REAL DEFAULT 0,
    duration REAL DEFAULT 0,
    last_watched DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    UNIQUE(user_id, movie_id, episode)
);
```

## 步骤 4: 创建索引（逐个执行）

```sql
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);
```

```sql
CREATE INDEX IF NOT EXISTS idx_favorites_movie_id ON favorites(movie_id);
```

```sql
CREATE INDEX IF NOT EXISTS idx_watch_history_user_id ON watch_history(user_id);
```

```sql
CREATE INDEX IF NOT EXISTS idx_watch_history_movie_id ON watch_history(movie_id);
```

```sql
CREATE INDEX IF NOT EXISTS idx_watch_history_last_watched ON watch_history(last_watched);
```

## 步骤 5: 插入管理员用户（可选）

如果你想在数据库中预先创建管理员用户，可以执行：

```sql
INSERT INTO users (username, password, is_admin) VALUES ('admin', 'your_password_here', 1);
```

**注意**: 请将 `your_password_here` 替换为你的实际密码。

## 验证安装

执行以下查询来验证表是否创建成功：

```sql
SELECT name FROM sqlite_master WHERE type='table';
```

应该返回：
- users
- favorites  
- watch_history

## 故障排除

如果遇到错误：
1. 确保每次只执行一个 SQL 语句
2. 等待每个语句执行完成后再执行下一个
3. 如果某个语句失败，可以重新执行（使用了 IF NOT EXISTS）

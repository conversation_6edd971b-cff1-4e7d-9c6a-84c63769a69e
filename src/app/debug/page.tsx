'use client';

import { useEffect, useState } from 'react';

interface DebugInfo {
  storageType: string;
  hasUsername: boolean;
  hasPassword: boolean;
  authInfo: any;
  serverConfig: any;
}

export default function DebugPage() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchDebugInfo() {
      try {
        // 获取服务器配置
        const serverConfigRes = await fetch('/api/server-config');
        const serverConfig = await serverConfigRes.json();

        // 获取认证信息
        const authCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('auth='));
        
        let authInfo = null;
        if (authCookie) {
          try {
            const authValue = authCookie.split('=')[1];
            authInfo = JSON.parse(decodeURIComponent(authValue));
          } catch (e) {
            authInfo = { error: 'Failed to parse auth cookie' };
          }
        }

        setDebugInfo({
          storageType: serverConfig.StorageType || 'unknown',
          hasUsername: !!process.env.NEXT_PUBLIC_USERNAME,
          hasPassword: !!process.env.NEXT_PUBLIC_PASSWORD,
          authInfo,
          serverConfig,
        });
      } catch (error) {
        console.error('Debug info fetch failed:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchDebugInfo();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载调试信息...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          系统调试信息
        </h1>

        <div className="space-y-6">
          {/* 存储配置 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              存储配置
            </h2>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">存储类型:</span>{' '}
                <span className={`px-2 py-1 rounded text-xs ${
                  debugInfo?.storageType === 'localstorage' 
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                }`}>
                  {debugInfo?.storageType}
                </span>
              </p>
              {debugInfo?.storageType === 'localstorage' && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded p-3 mt-2">
                  <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                    ⚠️ 当前使用 localstorage 模式，管理员设置功能不可用。
                    <br />
                    请设置环境变量 NEXT_PUBLIC_STORAGE_TYPE 为 upstash 或其他数据库类型。
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 认证信息 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              认证信息
            </h2>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">认证状态:</span>{' '}
                <span className={`px-2 py-1 rounded text-xs ${
                  debugInfo?.authInfo 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {debugInfo?.authInfo ? '已登录' : '未登录'}
                </span>
              </p>
              {debugInfo?.authInfo && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded p-3 mt-2">
                  <pre className="text-xs text-gray-600 dark:text-gray-300 overflow-auto">
                    {JSON.stringify(debugInfo.authInfo, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* 服务器配置 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              服务器配置
            </h2>
            <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
              <pre className="text-xs text-gray-600 dark:text-gray-300 overflow-auto">
                {JSON.stringify(debugInfo?.serverConfig, null, 2)}
              </pre>
            </div>
          </div>

          {/* 解决方案 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-4">
              解决方案
            </h2>
            <div className="space-y-3 text-sm text-blue-800 dark:text-blue-200">
              <p>如果管理员设置打不开，请检查以下配置：</p>
              <ol className="list-decimal list-inside space-y-2 ml-4">
                <li>确保在 Vercel 环境变量中设置了 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">NEXT_PUBLIC_STORAGE_TYPE</code> 为 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">upstash</code></li>
                <li>确保设置了 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">USERNAME</code> 和 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">PASSWORD</code></li>
                <li>如果使用 Upstash，确保设置了 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">UPSTASH_URL</code> 和 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">UPSTASH_TOKEN</code></li>
                <li>重新部署项目以应用环境变量更改</li>
              </ol>
            </div>
          </div>

          {/* 快速链接 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              快速链接
            </h2>
            <div className="space-y-2">
              <a 
                href="/login" 
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                登录页面
              </a>
              <a 
                href="/admin" 
                className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors ml-2"
              >
                管理员设置
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
